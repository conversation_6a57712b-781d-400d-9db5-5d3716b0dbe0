import hashlib
import os
import platform
import sys
import json
import threading
import tkinter as tk
from tkinter import messagebox, ttk
import customtkinter as ctk

try:
    import pystray
    from PIL import Image, ImageDraw
except ImportError:
    pystray = None

try:
    import darkdetect
except ImportError:
    darkdetect = None

import requests
import win32security
from Crypto.Cipher import AES
from Crypto.Hash import SHA256
from Crypto.Util.Padding import pad, unpad
import winreg
from pathlib import Path
import vdf
import sqlite3
from datetime import datetime
from keyauth import api
from cryptography.fernet import Fernet
import subprocess
import time
import webbrowser

# Configure customtkinter
ctk.set_appearance_mode("system")  # Auto-detect system theme
ctk.set_default_color_theme("blue")

class ThemeManager:
    @staticmethod
    def detect_system_theme():
        """Detect system theme preference"""
        if darkdetect:
            theme = darkdetect.theme()
            return "dark" if theme == "Dark" else "light"
        
        # Fallback for Windows
        if platform.system() == "Windows":
            try:
                import winreg
                registry = winreg.ConnectRegistry(None, winreg.HKEY_CURRENT_USER)
                key = winreg.OpenKey(registry, r"Software\Microsoft\Windows\CurrentVersion\Themes\Personalize")
                value, _ = winreg.QueryValueEx(key, "AppsUseLightTheme")
                winreg.CloseKey(key)
                return "light" if value == 1 else "dark"
            except:
                pass
        
        return "system"  # Default to system detection
    
    @staticmethod
    def apply_theme(theme="system"):
        """Apply theme to the application"""
        if theme == "auto":
            theme = ThemeManager.detect_system_theme()
        ctk.set_appearance_mode(theme)

class Colors:
    @staticmethod
    def get_colors():
        """Get theme-appropriate colors"""
        mode = ctk.get_appearance_mode()
        if mode == "Dark":
            return {
                'success': '#4CAF50',
                'error': '#F44336',
                'warning': '#FF9800',
                'info': '#2196F3',
                'text': '#FFFFFF',
                'bg': '#212121'
            }
        else:
            return {
                'success': '#388E3C',
                'error': '#D32F2F', 
                'warning': '#F57C00',
                'info': '#1976D2',
                'text': '#000000',
                'bg': '#FFFFFF'
            }

def getSteamPath():
    reg_key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, "Software\\Valve\\Steam")
    steam_path = winreg.QueryValueEx(reg_key, "SteamPath")[0]
    winreg.CloseKey(reg_key)

    if steam_path is None:
        print("[MTYB] Steam is not installed.")
        time.sleep(5)
        exit(0)

    return steam_path


# Define the server URL
key = ""
STEAM_PATH = getSteamPath()
SERVER_URL = "https://manifest.online-mtyb.com"
DATABASE_FILE = os.path.join(STEAM_PATH, 'app.log')
KEY_FILE = os.path.join(STEAM_PATH, 'app.info.log')
LOCK_FILE = os.path.join(STEAM_PATH, 'monitor.lock')

def generate_key():
    key = Fernet.generate_key()
    with open(KEY_FILE, 'wb') as key_file:
        key_file.write(key)

# Load the key from the file
def load_key():
    return open(KEY_FILE, 'rb').read()


# Encrypt a message
def encrypt_message(message, key):
    f = Fernet(key)
    return f.encrypt(message.encode())


# Decrypt a message
def decrypt_message(encrypted_message, key):
    f = Fernet(key)
    return f.decrypt(encrypted_message).decode()


# Generate and load key (only needed once)
if not os.path.exists(KEY_FILE):
    generate_key()
KEY = load_key()


def getchecksum():
    try:
        md5_hash = hashlib.md5()
        script_path = ''.join(sys.argv[0])
        if script_path == '-c':
            script_path = __file__
        file = open(script_path, "rb")
        md5_hash.update(file.read())
        file.close()
        digest = md5_hash.hexdigest()
        return digest
    except (FileNotFoundError, OSError):
        return "default_hash"


keyauthapp = api(
    name="MainSteam",
    ownerid="1tGVnUKtzH",
    secret="eb95192c2d44019fc97805ceb1986dcc70f9c54ccffa1cebce98973ab74a669f",
    version="1.0",
    hash_to_check=getchecksum()
)

SERVER_URL = keyauthapp.var("SERVER_API")

def ensure_dir_exists(path):
    if not os.path.exists(path):
        os.makedirs(path)


def get_manifest(app_id):
    response = requests.get(f"{SERVER_URL}/get_manifest?app_id={app_id}")
    if response.status_code == 200:
        return response.json()
    else:
        print(f"Failed to retrieve data for {app_id}")
        return None

def clearCache():
    steam_path = getSteamPath()
    appinfo_path = os.path.join(steam_path, 'appcache', 'appinfo.vdf')
    packageinfo_path = os.path.join(steam_path, 'appcache', 'packageinfo.vdf')

    # 检查文件是否存在并删除它们
    if os.path.exists(appinfo_path):
        os.remove(appinfo_path)

    if os.path.exists(packageinfo_path):
        os.remove(packageinfo_path)

def remove_lock():
    if os.path.exists(LOCK_FILE):
        os.remove(LOCK_FILE)

def update_client_config(app_id, client_base_dir):
    data = {
        'app_id': app_id,
        'password': "gAAAAABmiNHv4WzHJFpA_dPbVv1KrGIMYRvAReNcWAxZ5WVDZbOYcSHl81JwVpuZjUY_kobe63iHHSjeQJrsMWndlwbVVXtAKw=="
    }

    response = requests.post(f"{SERVER_URL}/update_client_config", json=data)

    if response.status_code == 200:
        print("[MTYB] Config Updated Successfully ✅")
    else:
        print("[MTYB] Failed To Update Client Config ❌")
        return

    client_config_path = Path(client_base_dir) / 'config' / 'config.vdf'

    if not client_config_path.exists():
        print("[MTYB] Client Config Not Found ❌")
        return

    server_depots = response.json()

    # Load client config
    with open(client_config_path, 'r', encoding='utf-8') as f:
        client_config = vdf.load(f)

    # Ensure the necessary structure exists in client config
    depots_path = ["InstallConfigStore", "Software", "Valve", "Steam", "depots"]
    current = client_config
    for key in depots_path:
        if key not in current:
            current[key] = {}
        current = current[key]

    # Update the client config depots with server depots
    current.update(server_depots)

    # Write the updated client config back to file
    with open(client_config_path, 'w', encoding='utf-8') as f:
        vdf.dump(client_config, f, pretty=True)


def download_manifest(app_id, manifest_file, download_path):
    response = requests.get(f"{SERVER_URL}/download_manifest/{app_id}/{manifest_file}?password=gAAAAABmiNHv4WzHJFpA_dPbVv1KrGIMYRvAReNcWAxZ5WVDZbOYcSHl81JwVpuZjUY_kobe63iHHSjeQJrsMWndlwbVVXtAKw==")
    if response.status_code == 200:
        with open(download_path, 'wb') as f:
            f.write(response.content)
    else:
        print("[MTYB] Failed To Download File ❌")


def get_connection():
    return sqlite3.connect(DATABASE_FILE)


def create_license(license_key, app_id):
    conn = get_connection()
    cursor = conn.cursor()
    created_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

    encrypted_license_key = encrypt_message(license_key, KEY)
    encrypted_app_id = encrypt_message(app_id, KEY)

    cursor.execute('''
        INSERT INTO licenses (license_key, created_date, app_id)
        VALUES (?, ?, ?)
    ''', (encrypted_license_key, created_date, encrypted_app_id))

    conn.commit()

    # Fetch the app info and its DLCs
    data = keyauthapp.var("Main")
    app_info = {}

    if data is not None:
        json_data = json.loads(data)
        for app in json_data["apps"]:
            if license_key.startswith(app["license_prefix"]):
                app_info = app

    if app_info:
        dlcs = keyauthapp.var(app_info["app_name"])
        if dlcs is not None:
            dlcs_data = json.loads(dlcs)
            for dlc_id in dlcs_data["dlcs"]:
                cursor.execute('''
                        INSERT INTO dlcs (license_key, dlc_id)
                        VALUES (?, ?)
                    ''', (encrypted_license_key, encrypt_message(str(dlc_id), KEY)))

    conn.commit()

    conn.close()
    print("[MTYB] License recognized successfully ✅")


def license_exists(license_key):
    conn = get_connection()
    cursor = conn.cursor()
    cursor.execute('''
            SELECT * FROM licenses
        ''')

    rows = cursor.fetchall()
    conn.close()

    if rows:
        for row in rows:
            decrypted_license_key = decrypt_message(row[0], KEY)
            if(decrypted_license_key == license_key):
                return True

    return False

def get_first_license():
    conn = get_connection()
    cursor = conn.cursor()

    cursor.execute('''
            SELECT * FROM licenses 
        ''')

    row = cursor.fetchone()
    conn.close()

    if row:
        decrypted_license_key = decrypt_message(row[0], KEY)
        decrypted_app_id = decrypt_message(row[2], KEY)
        return {'license_key': decrypted_license_key, 'created_date': row[1], 'app_id': decrypted_app_id}
    else:
        print("First License Not Found.")
        return None

def read_all_license():
    conn = get_connection()
    cursor = conn.cursor()

    cursor.execute('''
        SELECT * FROM licenses
    ''')

    rows = cursor.fetchall()
    conn.close()

    if rows:
        licenses = []
        for row in rows:
            decrypted_license_key = decrypt_message(row[0], KEY)
            decrypted_app_id = decrypt_message(row[2], KEY)
            licenses.append({'license_key': decrypted_license_key, 'created_date': row[1], 'app_id': decrypted_app_id})
        return licenses
    else:
        print("License not found.")
        return None

def read_license(license_key):
    conn = get_connection()
    cursor = conn.cursor()

    encrypted_license_key = encrypt_message(license_key, KEY)

    cursor.execute('''
        SELECT * FROM licenses
        ORDER BY created_date DESC
    ''')

    rows = cursor.fetchall()
    conn.close()

    if rows:
        for row in rows:
            decrypted_license_key = decrypt_message(row[0], KEY)
            decrypted_app_id = decrypt_message(row[2], KEY)
            return {'license_key': decrypted_license_key, 'created_date': row[1], 'app_id': decrypted_app_id}
    else:
        print("License not found.")
        return None


def read_all_app_id():
    conn = get_connection()
    cursor = conn.cursor()

    cursor.execute('''
        SELECT license_key, created_date, app_id FROM licenses
    ''')

    rows = cursor.fetchall()
    conn.close()

    if rows:
        app_ids = []
        for row in rows:
            decrypted_app_id = decrypt_message(row[2], KEY)
            app_ids.append(decrypted_app_id)
        return app_ids
    else:
        print("[MTYB] No Licenses.")
        return None


def update_license(license_key, new_app_id):
    conn = get_connection()
    cursor = conn.cursor()

    encrypted_license_key = encrypt_message(license_key, KEY)
    encrypted_new_app_id = encrypt_message(new_app_id, KEY)

    cursor.execute('''
        UPDATE licenses SET app_id = ? WHERE license_key = ?
    ''', (encrypted_new_app_id, encrypted_license_key))

    conn.commit()
    conn.close()
    print("License updated successfully.")


def delete_license(license_key):
    conn = get_connection()
    cursor = conn.cursor()

    encrypted_license_key = encrypt_message(license_key, KEY)

    cursor.execute('''
        DELETE FROM licenses WHERE license_key = ?
    ''', (encrypted_license_key,))

    conn.commit()
    conn.close()
    print("License deleted successfully.")


def main(app_id):
    try:
        steam_path = STEAM_PATH

        if steam_path is None:
            print("Steam is not installed.")
            return

        client_base_path = Path(steam_path)

        # Ensure client base path exists
        ensure_dir_exists(client_base_path)

        # Retrieve manifest files and config.vdf content from server
        manifest_data = get_manifest(app_id)
        if manifest_data is None:
            return

        # Ensure client's depotcache directory exists
        client_depotcache_path = client_base_path / 'depotcache'
        ensure_dir_exists(client_depotcache_path)

        # Append server config.vdf content to client's config.vdf
        update_client_config(app_id, client_base_path)

        # Download manifest files from server to client's depotcache directory
        for manifest_file in manifest_data['manifests']:
            download_path = client_depotcache_path / manifest_file
            download_manifest(app_id, manifest_file, download_path)

    except Exception as e:
        print(f"Error: {str(e)}")


def update(app_id):
    try:
        steam_path = STEAM_PATH

        if steam_path is None:
            print("Steam is not installed.")
            return

        client_base_path = Path(steam_path)

        # Ensure client base path exists
        ensure_dir_exists(client_base_path)

        # Retrieve manifest files and config.vdf content from server
        manifest_data = get_manifest(app_id)
        if manifest_data is None:
            return

        # Ensure client's depotcache directory exists
        client_depotcache_path = client_base_path / 'depotcache'
        ensure_dir_exists(client_depotcache_path)

        # Append server config.vdf content to client's config.vdf
        update_client_config(app_id, client_base_path)

        # Download manifest files from server to client's depotcache directory
        for manifest_file in manifest_data['manifests']:
            download_path = client_depotcache_path / manifest_file
            download_manifest(app_id, manifest_file, download_path)

    except Exception as e:
        print(f"Error: {str(e)}")


def authenticate():
    global key
    key = input(' Enter License Key : ')
    isLoggedIn = keyauthapp.license(key)
    authenticated = False
    if isLoggedIn:
        print("[MTYB] Checking Relevant Information... ⏳")
        authenticated = True

        data = keyauthapp.var("Main")
        app_info = {}

        if (data != None):
            json_data = json.loads(data)
            for app in json_data["apps"]:
                if key.startswith(app["license_prefix"]):
                    app_info = app
        else:
            print("Data Is Not Found, Please Contact Seller.")

        if app_info != {}:
            if (license_exists(key) == False and app_info.get('app_id') != None):
                create_license(key, app_info.get('app_id'))

        else:
            print("App Not Found.")

    return authenticated


def checkDatabase():
    if not os.path.exists(DATABASE_FILE):
        print(f"{DATABASE_FILE} does not exist. Creating a new database.")

        conn = get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS licenses (
                license_key BLOB PRIMARY KEY,
                created_date TEXT,
                app_id BLOB
            )
        ''')

        conn.commit()

        cursor.execute('''
                CREATE TABLE IF NOT EXISTS dlcs (
                    license_key TEXT,
                    dlc_id TEXT,
                    FOREIGN KEY(license_key) REFERENCES licenses(license_key)
                )
            ''')

        conn.commit()
        conn.close()

        print("Database created successfully with an empty table.")

def convert_to_windows_path(file_path):
    return file_path.replace("/", "\\")

def add_defender_exception(exclusion_path):
    try:
        # Use PowerShell command to add the exclusion path
        powershell_cmd = f"Add-MpPreference -ExclusionPath '{exclusion_path}'"
        subprocess.run(["powershell", "-Command", powershell_cmd], check=True)
        print(f"Windows Defender exclusion added for {exclusion_path}.")
    except subprocess.CalledProcessError as e:
        pass

def checkMiniTool():
    steam_path = getSteamPath()
    steam_path = Path(steam_path)
    filename = "steamclient32.dll"
    target_file = steam_path / filename

    reset = keyauthapp.var("Reset")
    run = True

    if reset == "True":
        run = True

    if not os.path.exists(target_file):
        run = True

    if run:
        url = keyauthapp.var("MiniTool_steamclient32.dll")
        response = requests.get(url)
        if response.status_code == 200:
            # Open a file and write the content of the response to the target location
            with open(target_file, 'wb') as f:
                f.write(response.content)
        else:
            print(f"Failed to download file. Status code: {response.status_code}")


def checkGreenLuma():
    steam_path = getSteamPath()
    steam_path = Path(steam_path)
    filename = "User32.dll"
    target_file = steam_path / filename
    reset = keyauthapp.var("Reset")
    run = False

    if reset == "True":
        run = True

    if not os.path.exists(target_file):
        run = True

    if run:
        url = keyauthapp.var("GreenLuma_User32.dll")
        response = requests.get(url)
        if response.status_code == 200:
            # Open a file and write the content of the response to the target location
            with open(target_file, 'wb') as f:
                f.write(response.content)
        else:
            print(f"Failed to download file. Status code: {response.status_code}")

    licenses = read_all_license()
    main_info = json.loads(keyauthapp.var("Main"))
    app_names = []
    dlcs_list = []
    if len(licenses) > 0:
        for license in licenses:
            if keyauthapp.license(license['license_key']) != False:
                for app in main_info["apps"]:
                    if app["app_id"] == license["app_id"]:
                        app_names.append(app["app_name"])

        # 获取DLCs列表
        for app_name in app_names:
            data = keyauthapp.var(app_name)
            if data is not None:
                data = json.loads(keyauthapp.var(app_name))
                dlcs_list.extend(data["dlcs"])

    # 创建存储DLCs ID的目录
    steam_path = Path(getSteamPath()) / "AppList"
    os.makedirs(steam_path, exist_ok=True)

    # 将DLCs ID写入文件
    for i, dlc_id in enumerate(dlcs_list):
        with open(os.path.join(steam_path, f"{i}.txt"), 'w') as file:
            file.write(str(dlc_id))


def addDefenderExclusion():
    try:
        exclusion_path = convert_to_windows_path(STEAM_PATH) + r"\*"
        add_defender_exception(exclusion_path)
    except Exception as e:
        print("[MTYB] Please disable your anti-virus. It blocked the tool.")
        time.sleep(5)
        exit(0)


def checkKoaLoader():
    steam_path = getSteamPath()
    steam_path = Path(steam_path)
    filename = 'hid.dll'
    unlocker_name = 'Lyptus.dll'
    configname = "Koaloader.config.json"
    target_file = steam_path / filename
    target_unlocker = steam_path / unlocker_name
    target_config = steam_path / configname
    reset = keyauthapp.var("Reset")
    run = False

    if reset == "True":
        run = True

    if not os.path.exists(target_file):
        run = True

    if not os.path.exists(target_config):
        run = True

    if run:
        url = keyauthapp.var("KoaLoader_hid.dll")
        response = requests.get(url)
        if response.status_code == 200:
            # Open a file and write the content of the response to the target location
            with open(target_file, 'wb') as f:
                f.write(response.content)
        else:
            print(f"Failed to download file. Status code: {response.status_code}")

        url = keyauthapp.var("KoaLoader_config")
        response = requests.get(url)
        if response.status_code == 200:
            # Open a file and write the content of the response to the target location
            with open(target_config, 'wb') as f:
                f.write(response.content)
        else:
            print(f"Failed to download file. Status code: {response.status_code}")

        url = keyauthapp.var("Starter_Lyptus.dll")
        response = requests.get(url)
        if response.status_code == 200:
            # Open a file and write the content of the response to the target location
            with open(target_unlocker, 'wb') as f:
                f.write(response.content)
        else:
            print(f"Failed to download file. Status code: {response.status_code}")


class SteamManifestGUI:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("MTYB Steam Manifest Tool")
        self.root.geometry("800x600")
        self.root.minsize(600, 400)
        
        # Apply system theme
        ThemeManager.apply_theme("system")
        
        self.authenticated = False
        self.current_license = None
        self.setup_ui()
        self.initialize_app()
        # Setup system tray (optional)
        try:
            self.setup_system_tray()
        except:
            pass
    
    def setup_ui(self):
        """Setup the main UI components"""
        # Create main container with padding
        main_frame = ctk.CTkFrame(self.root)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Title
        title_label = ctk.CTkLabel(
            main_frame, 
            text="MTYB Steam Manifest Tool", 
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=(0, 20))
        
        # Create tabview
        self.tabview = ctk.CTkTabview(main_frame, width=700, height=400)
        self.tabview.pack(fill="both", expand=True)
        
        # Add tabs
        self.setup_auth_tab()
        self.setup_manifest_tab()
        self.setup_licenses_tab()
        self.setup_logs_tab()
        self.setup_settings_tab()
        
        # Status bar
        self.status_frame = ctk.CTkFrame(main_frame)
        self.status_frame.pack(fill="x", pady=(10, 0))
        
        self.status_label = ctk.CTkLabel(
            self.status_frame, 
            text="Status: Not authenticated", 
            font=ctk.CTkFont(size=12)
        )
        self.status_label.pack(side="left", padx=10, pady=5)
        
        # Theme toggle button
        self.theme_button = ctk.CTkButton(
            self.status_frame,
            text="Toggle Theme",
            width=100,
            command=self.toggle_theme
        )
        self.theme_button.pack(side="right", padx=10, pady=5)
    
    def setup_auth_tab(self):
        """Setup authentication tab"""
        auth_tab = self.tabview.add("Authentication")
        
        # Authentication frame
        auth_frame = ctk.CTkFrame(auth_tab)
        auth_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # License key input
        ctk.CTkLabel(auth_frame, text="License Key:", font=ctk.CTkFont(size=14, weight="bold")).pack(pady=(20, 5))
        
        self.license_entry = ctk.CTkEntry(
            auth_frame, 
            placeholder_text="Enter your license key",
            width=400,
            height=40
        )
        self.license_entry.pack(pady=(0, 20))
        
        # Auth button
        self.auth_button = ctk.CTkButton(
            auth_frame,
            text="Authenticate",
            width=200,
            height=40,
            command=self.authenticate_license
        )
        self.auth_button.pack(pady=10)
        
        # Status display
        self.auth_status = ctk.CTkTextbox(auth_frame, height=200, width=500)
        self.auth_status.pack(fill="both", expand=True, pady=20)
        self.auth_status.insert("0.0", "Enter your license key and click Authenticate to begin.")
    
    def setup_manifest_tab(self):
        """Setup manifest management tab"""
        manifest_tab = self.tabview.add("Manifest Management")
        
        # Controls frame
        controls_frame = ctk.CTkFrame(manifest_tab)
        controls_frame.pack(fill="x", padx=20, pady=20)
        
        # App ID input
        ctk.CTkLabel(controls_frame, text="App ID:", font=ctk.CTkFont(size=14, weight="bold")).pack(side="left", padx=(20, 10))
        
        self.app_id_entry = ctk.CTkEntry(
            controls_frame,
            placeholder_text="Enter Steam App ID",
            width=200
        )
        self.app_id_entry.pack(side="left", padx=10)
        
        # Download button
        self.download_button = ctk.CTkButton(
            controls_frame,
            text="Download Manifest",
            command=self.download_manifest,
            state="disabled"
        )
        self.download_button.pack(side="left", padx=10)
        
        # Update all button
        self.update_all_button = ctk.CTkButton(
            controls_frame,
            text="Update All",
            command=self.update_all_manifests,
            state="disabled"
        )
        self.update_all_button.pack(side="left", padx=10)
        
        # Launch Steam button
        self.launch_button = ctk.CTkButton(
            controls_frame,
            text="Launch Steam",
            command=self.launch_steam,
            state="disabled"
        )
        self.launch_button.pack(side="right", padx=20)
        
        # Progress frame
        progress_frame = ctk.CTkFrame(manifest_tab)
        progress_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        self.progress_label = ctk.CTkLabel(progress_frame, text="Ready")
        self.progress_label.pack(pady=10)
        
        self.progress_bar = ctk.CTkProgressBar(progress_frame, width=400)
        self.progress_bar.pack(pady=10)
        self.progress_bar.set(0)
        
        # Manifest info display
        self.manifest_info = ctk.CTkTextbox(manifest_tab, height=250)
        self.manifest_info.pack(fill="both", expand=True, padx=20, pady=(0, 20))
    
    def setup_licenses_tab(self):
        """Setup license management tab"""
        licenses_tab = self.tabview.add("License Management")
        
        # License list frame
        list_frame = ctk.CTkFrame(licenses_tab)
        list_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        ctk.CTkLabel(list_frame, text="Registered Licenses", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=(10, 20))
        
        # License display
        self.license_display = ctk.CTkTextbox(list_frame, height=300)
        self.license_display.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        # Buttons frame
        buttons_frame = ctk.CTkFrame(list_frame)
        buttons_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        self.refresh_licenses_button = ctk.CTkButton(
            buttons_frame,
            text="Refresh List",
            command=self.refresh_license_list
        )
        self.refresh_licenses_button.pack(side="left", padx=10)
    
    def setup_logs_tab(self):
        """Setup logging tab"""
        logs_tab = self.tabview.add("Logs")
        
        # Log display
        self.log_display = ctk.CTkTextbox(logs_tab, height=400)
        self.log_display.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Log controls
        log_controls = ctk.CTkFrame(logs_tab)
        log_controls.pack(fill="x", padx=20, pady=(0, 20))
        
        self.clear_logs_button = ctk.CTkButton(
            log_controls,
            text="Clear Logs",
            command=self.clear_logs
        )
        self.clear_logs_button.pack(side="left", padx=10)
    
    def setup_settings_tab(self):
        """Setup settings tab"""
        settings_tab = self.tabview.add("Settings")
        
        settings_frame = ctk.CTkFrame(settings_tab)
        settings_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Theme settings
        ctk.CTkLabel(settings_frame, text="Appearance", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=(20, 10))
        
        theme_frame = ctk.CTkFrame(settings_frame)
        theme_frame.pack(fill="x", padx=20, pady=10)
        
        ctk.CTkLabel(theme_frame, text="Theme:").pack(side="left", padx=20)
        
        self.theme_var = ctk.StringVar(value="System")
        self.theme_menu = ctk.CTkOptionMenu(
            theme_frame,
            values=["System", "Light", "Dark"],
            variable=self.theme_var,
            command=self.change_theme
        )
        self.theme_menu.pack(side="left", padx=10)
        
        # Steam path display
        ctk.CTkLabel(settings_frame, text="Steam Configuration", font=ctk.CTkFont(size=16, weight="bold")).pack(pady=(20, 10))
        
        steam_frame = ctk.CTkFrame(settings_frame)
        steam_frame.pack(fill="x", padx=20, pady=10)
        
        try:
            steam_path = getSteamPath()
            path_text = f"Steam Path: {steam_path}"
        except:
            path_text = "Steam Path: Not detected"
        
        ctk.CTkLabel(steam_frame, text=path_text).pack(padx=20, pady=10)
    
    def initialize_app(self):
        """Initialize the application"""
        self.log_message("[MTYB] Tools Initializing...")
        
        # Run initialization in background thread
        threading.Thread(target=self._background_init, daemon=True).start()
    
    def _background_init(self):
        """Background initialization tasks"""
        try:
            addDefenderExclusion()
            checkDatabase()
            self.log_message("[MTYB] Initialization complete")
        except Exception as e:
            self.log_message(f"[MTYB] Initialization error: {str(e)}")
    
    def log_message(self, message):
        """Add message to log display"""
        def update_log():
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_entry = f"[{timestamp}] {message}\n"
            self.log_display.insert("end", log_entry)
            self.log_display.see("end")
            
            # Also update auth status if we're on that tab
            if hasattr(self, 'auth_status'):
                self.auth_status.insert("end", log_entry)
                self.auth_status.see("end")
        
        # Ensure this runs on the main thread
        self.root.after(0, update_log)
    
    def authenticate_license(self):
        """Authenticate the entered license key"""
        license_key = self.license_entry.get().strip()
        if not license_key:
            messagebox.showerror("Error", "Please enter a license key")
            return
        
        self.auth_button.configure(state="disabled", text="Authenticating...")
        
        # Run authentication in background thread
        threading.Thread(
            target=self._authenticate_background, 
            args=(license_key,), 
            daemon=True
        ).start()
    
    def _authenticate_background(self, license_key):
        """Background authentication process"""
        try:
            global key
            key = license_key
            
            self.log_message(f"[MTYB] Authenticating license: {license_key[:8]}...")
            
            isLoggedIn = keyauthapp.license(key)
            
            if isLoggedIn:
                self.log_message("[MTYB] Checking relevant information...")
                
                data = keyauthapp.var("Main")
                app_info = {}
                
                if data is not None:
                    json_data = json.loads(data)
                    for app in json_data["apps"]:
                        if key.startswith(app["license_prefix"]):
                            app_info = app
                            break
                else:
                    self.log_message("[MTYB] Data not found, please contact seller.")
                    self._auth_failed()
                    return
                
                if app_info:
                    if not license_exists(key) and app_info.get('app_id'):
                        create_license(key, app_info.get('app_id'))
                    
                    self.authenticated = True
                    self.current_license = app_info
                    self.log_message("[MTYB] Authentication successful!")
                    
                    # Enable UI elements
                    self.root.after(0, self._auth_success)
                    
                    # Run post-auth setup
                    self._post_auth_setup()
                    
                else:
                    self.log_message("[MTYB] App not found.")
                    self._auth_failed()
            else:
                self.log_message("[MTYB] Authentication failed.")
                self._auth_failed()
                
        except Exception as e:
            self.log_message(f"[MTYB] Authentication error: {str(e)}")
            self._auth_failed()
    
    def _auth_success(self):
        """Update UI after successful authentication"""
        self.auth_button.configure(state="normal", text="Authenticated ✓")
        self.status_label.configure(text="Status: Authenticated")
        
        # Enable manifest management
        self.download_button.configure(state="normal")
        self.update_all_button.configure(state="normal")
        self.launch_button.configure(state="normal")
        
        # Switch to manifest tab
        self.tabview.set("Manifest Management")
        
        # Refresh license list
        self.refresh_license_list()
    
    def _auth_failed(self):
        """Update UI after failed authentication"""
        self.root.after(0, lambda: self.auth_button.configure(state="normal", text="Authenticate"))
    
    def _post_auth_setup(self):
        """Run post-authentication setup tasks"""
        try:
            remove_lock()
            clearCache()
            checkKoaLoader()
            checkGreenLuma()
            checkMiniTool()
            self.log_message("[MTYB] Setup complete")
        except Exception as e:
            self.log_message(f"[MTYB] Setup error: {str(e)}")
    
    def download_manifest(self):
        """Download manifest for specified app ID"""
        app_id = self.app_id_entry.get().strip()
        if not app_id:
            messagebox.showerror("Error", "Please enter an App ID")
            return
        
        if not self.authenticated:
            messagebox.showerror("Error", "Please authenticate first")
            return
        
        # Run download in background
        threading.Thread(
            target=self._download_manifest_background,
            args=(app_id,),
            daemon=True
        ).start()
    
    def _download_manifest_background(self, app_id):
        """Background manifest download"""
        try:
            self.log_message(f"[MTYB] Starting download for App ID: {app_id}")
            self.root.after(0, lambda: self.progress_bar.set(0.2))
            self.root.after(0, lambda: self.progress_label.configure(text="Downloading manifest..."))
            
            main(app_id)
            
            self.root.after(0, lambda: self.progress_bar.set(1.0))
            self.root.after(0, lambda: self.progress_label.configure(text="Download complete!"))
            self.log_message(f"[MTYB] Download complete for App ID: {app_id}")
            
            # Update manifest info
            self._update_manifest_info(app_id)
            
        except Exception as e:
            self.log_message(f"[MTYB] Download error: {str(e)}")
            self.root.after(0, lambda: self.progress_label.configure(text="Download failed"))
            self.root.after(0, lambda: self.progress_bar.set(0))
    
    def update_all_manifests(self):
        """Update all registered app manifests"""
        if not self.authenticated:
            messagebox.showerror("Error", "Please authenticate first")
            return
        
        threading.Thread(target=self._update_all_background, daemon=True).start()
    
    def _update_all_background(self):
        """Background update all manifests"""
        try:
            app_ids = read_all_app_id()
            if not app_ids:
                self.log_message("[MTYB] No licenses found")
                return
            
            total = len(app_ids)
            for i, app_id in enumerate(app_ids):
                progress = (i + 1) / total
                self.root.after(0, lambda p=progress: self.progress_bar.set(p))
                self.root.after(0, lambda a=app_id: self.progress_label.configure(text=f"Updating {a}..."))
                
                self.log_message(f"[MTYB] Updating App ID: {app_id}")
                update(app_id)
            
            self.root.after(0, lambda: self.progress_label.configure(text="All updates complete!"))
            self.log_message("[MTYB] All manifests updated")
            
        except Exception as e:
            self.log_message(f"[MTYB] Update error: {str(e)}")
    
    def launch_steam(self):
        """Launch Steam with current app"""
        app_id = self.app_id_entry.get().strip()
        if not app_id:
            # Try to get from current license
            lic = read_license(key) if self.authenticated else None
            if lic:
                app_id = lic['app_id']
            else:
                messagebox.showerror("Error", "Please enter an App ID")
                return
        
        try:
            webbrowser.open(f"steam://install/{app_id}")
            self.log_message(f"[MTYB] Launched Steam for App ID: {app_id}")
        except Exception as e:
            self.log_message(f"[MTYB] Launch error: {str(e)}")
    
    def refresh_license_list(self):
        """Refresh the license list display"""
        try:
            licenses = read_all_license()
            if licenses:
                display_text = "Registered Licenses:\n\n"
                for i, lic in enumerate(licenses, 1):
                    display_text += f"{i}. License: {lic['license_key'][:8]}...\n"
                    display_text += f"   App ID: {lic['app_id']}\n"
                    display_text += f"   Created: {lic['created_date']}\n\n"
            else:
                display_text = "No licenses registered."
            
            self.license_display.delete("0.0", "end")
            self.license_display.insert("0.0", display_text)
        except Exception as e:
            self.log_message(f"[MTYB] Error refreshing licenses: {str(e)}")
    
    def _update_manifest_info(self, app_id):
        """Update manifest information display"""
        try:
            manifest_data = get_manifest(app_id)
            if manifest_data:
                info_text = f"Manifest Information for App ID: {app_id}\n\n"
                info_text += f"Available Manifests: {len(manifest_data.get('manifests', []))}\n\n"
                
                for manifest in manifest_data.get('manifests', []):
                    info_text += f"- {manifest}\n"
                
                self.root.after(0, lambda: self._set_manifest_info(info_text))
        except Exception as e:
            self.log_message(f"[MTYB] Error updating manifest info: {str(e)}")
    
    def _set_manifest_info(self, text):
        """Set manifest info text (UI thread)"""
        self.manifest_info.delete("0.0", "end")
        self.manifest_info.insert("0.0", text)
    
    def clear_logs(self):
        """Clear the log display"""
        self.log_display.delete("0.0", "end")
    
    def toggle_theme(self):
        """Toggle between light and dark themes"""
        current = ctk.get_appearance_mode()
        new_theme = "Light" if current == "Dark" else "Dark"
        ctk.set_appearance_mode(new_theme)
        self.log_message(f"[MTYB] Switched to {new_theme} theme")
    
    def change_theme(self, choice):
        """Change theme based on dropdown selection"""
        theme_map = {
            "System": "system",
            "Light": "light", 
            "Dark": "dark"
        }
        
        theme = theme_map.get(choice, "system")
        ThemeManager.apply_theme(theme)
        self.log_message(f"[MTYB] Theme changed to: {choice}")
    
    def setup_system_tray(self):
        """Setup system tray integration (optional)"""
        try:
            import pystray
            from PIL import Image, ImageDraw
            
            # Create simple icon
            image = Image.new('RGB', (64, 64), color='blue')
            draw = ImageDraw.Draw(image)
            draw.text((20, 20), "M", fill='white')
            
            # Create menu
            menu = pystray.Menu(
                pystray.MenuItem("Show", self.show_from_tray),
                pystray.MenuItem("Hide", self.hide_to_tray),
                pystray.Menu.SEPARATOR,
                pystray.MenuItem("Exit", self.quit_from_tray)
            )
            
            self.tray_icon = pystray.Icon("MTYB Tool", image, menu=menu)
            
            # Setup window close behavior
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            
            # Start tray in background
            threading.Thread(target=self.tray_icon.run, daemon=True).start()
            
        except ImportError:
            # System tray is optional
            self.root.protocol("WM_DELETE_WINDOW", self.root.quit)
    
    def show_from_tray(self, icon=None, item=None):
        """Show window from system tray"""
        self.root.deiconify()
        self.root.lift()
    
    def hide_to_tray(self, icon=None, item=None):
        """Hide window to system tray"""
        self.root.withdraw()
    
    def quit_from_tray(self, icon=None, item=None):
        """Quit from system tray"""
        if hasattr(self, 'tray_icon') and self.tray_icon:
            self.tray_icon.stop()
        self.root.quit()
    
    def on_closing(self):
        """Handle window close event"""
        if hasattr(self, 'tray_icon') and self.tray_icon:
            # Hide to tray instead of closing
            self.root.withdraw()
            self.log_message("[MTYB] Application minimized to system tray")
        else:
            # No tray support, just quit
            self.root.quit()
    
    def run(self):
        """Start the GUI application"""
        self.root.mainloop()

def main_gui():
    """Main entry point for GUI version"""
    app = SteamManifestGUI()
    app.run()

if __name__ == '__main__':
    main_gui()